use std::collections::HashMap;
use std::sync::Mutex;

use log::{error, info, trace, warn};
use raw_window_handle::{HasW<PERSON>owHandle, RawWindowHandle};
use serde::de::DeserializeOwned;
use tauri::{plugin::Plugin<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, Runtime};

use crate::models::*;
use crate::mpv_wrapper_bindings::{MpvWrapper, MpvWrapperFormat};
use crate::{MpvExt, Result};

fn get_format_from_string(format_str: &str) -> Result<MpvWrapperFormat> {
    match format_str {
        "string" => Ok(MpvWrapperFormat::String),
        "flag" => Ok(MpvWrapperFormat::Flag),
        "int64" => Ok(MpvWrapperFormat::Int64),
        "double" => Ok(MpvWrapperFormat::Double),
        "node" => Ok(MpvWrapperFormat::Node),
        _ => Err(crate::Error::Format(format_str.to_string())),
    }
}

pub fn init<R: Runtime, C: DeserializeOwned>(
    app: &AppHandle<R>,
    _api: PluginApi<R, C>,
) -> crate::Result<Mpv<R>> {
    info!("Plugin registered.");
    let mpv = Mpv {
        app: app.clone(),
        instances: Mutex::new(HashMap::new()),
    };
    Ok(mpv)
}

pub struct Mpv<R: Runtime> {
    app: AppHandle<R>,
    pub instances: Mutex<HashMap<String, MpvInstance>>,
}

impl<R: Runtime> Mpv<R> {
    pub fn init(&self, mpv_config: MpvConfig, window_label: &str) -> Result<String> {
        let app = self.app.clone();

        if let Some(webview_window) = app.get_webview_window(window_label) {
            let handle_result = webview_window.window_handle();

            match handle_result {
                Ok(handle_wrapper) => {
                    let raw_handle = handle_wrapper.as_raw();
                    let window_handle = match raw_handle {
                        RawWindowHandle::Win32(handle) => handle.hwnd.get() as i64,
                        RawWindowHandle::Xlib(handle) => handle.window as i64,
                        RawWindowHandle::AppKit(handle) => handle.ns_view.as_ptr() as i64,
                        _ => {
                            error!(
                                "Unsupported window handle type for window '{}'.",
                                window_label
                            );
                            return Err(crate::Error::UnsupportedPlatform);
                        }
                    };

                    let mut instances_lock = app.mpv().instances.lock().unwrap();

                    if instances_lock.contains_key(window_label) {
                        info!(
                            "mpv instance for window '{}' already exists. Skipping initialization.",
                            window_label
                        );
                        return Ok(window_label.to_string());
                    }

                    info!("Initializing mpv instance for window '{}'...", window_label);

                    let mpv = MpvWrapper::new().ok_or_else(|| {
                        crate::Error::Initialization("Failed to create MPV instance".to_string())
                    })?;

                    // 设置初始选项
                    if let Some(options) = mpv_config.initial_options {
                        for (key, value) in options {
                            let value_str = match value {
                                serde_json::Value::Bool(b) => {
                                    if b { "yes" } else { "no" }.to_string()
                                }
                                serde_json::Value::Number(n) => n.to_string(),
                                serde_json::Value::String(s) => s,
                                _ => continue,
                            };
                            mpv.set_option(&key, &value_str)
                                .map_err(|e| crate::Error::Initialization(e.to_string()))?;
                        }
                    }

                    // 设置窗口ID
                    mpv.set_option("wid", &window_handle.to_string())
                        .map_err(|e| crate::Error::Initialization(e.to_string()))?;

                    // 初始化MPV
                    mpv.initialize()
                        .map_err(|e| crate::Error::Initialization(e.to_string()))?;

                    // 暂时不支持属性观察功能
                    if let Some(_observed_properties) = mpv_config.observed_properties {
                        warn!("Property observation is not yet supported with the wrapper implementation");
                        // TODO: 实现属性观察功能
                    }

                    info!("mpv instance initialized for window '{}'.", window_label,);

                    let instance = MpvInstance { mpv };
                    instances_lock.insert(window_label.to_string(), instance);

                    // 暂时不支持事件循环
                    warn!("Event loop is not yet supported with the wrapper implementation");

                    Ok(window_label.to_string())
                }
                Err(e) => {
                    error!(
                        "Failed to get raw window handle for window '{}': {:?}",
                        window_label, e
                    );
                    Err(crate::Error::WindowHandleError)
                }
            }
        } else {
            warn!(
                "Window with label '{}' not found. Please ensure the window exists.",
                window_label
            );
            Err(crate::Error::WindowHandleError)
        }
    }

    pub fn destroy(&self, window_label: &str) -> Result<()> {
        let instance_to_kill = {
            let mut instances_lock = match self.app.mpv().instances.lock() {
                Ok(guard) => guard,
                Err(poisoned) => {
                    warn!("Mutex for mpv instances was poisoned. Recovering.");
                    poisoned.into_inner()
                }
            };
            instances_lock.remove(window_label)
        };

        if let Some(instance) = instance_to_kill {
            match instance.mpv.command("quit", &[]) {
                Ok(_) => {
                    info!(
                        "mpv instance for window '{}' destroyed successfully.",
                        window_label,
                    );
                    Ok(())
                }
                Err(e) => {
                    let error_message = format!(
                        "Failed to destroy mpv instance for window '{}': {}",
                        window_label, e,
                    );
                    error!("{}", error_message);
                    Err(crate::Error::Mpv(error_message))
                }
            }
        } else {
            info!(
            "No running mpv instance found for window '{}' to destroy. It may have already terminated.",
            window_label
        );
            Ok(())
        }
    }

    pub fn command(
        &self,
        name: &str,
        args: &Vec<serde_json::Value>,
        window_label: &str,
    ) -> Result<()> {
        if args.is_empty() {
            trace!("COMMAND '{}'", name);
        } else {
            trace!("COMMAND '{}' '{:?}'", name, args);
        }

        let instances_lock = match self.app.mpv().instances.lock() {
            Ok(guard) => guard,
            Err(poisoned) => {
                warn!("Mutex was poisoned, recovering.");
                poisoned.into_inner()
            }
        };

        if let Some(instance) = instances_lock.get(window_label) {
            let string_args: Vec<String> = args
                .iter()
                .map(|v| match v {
                    serde_json::Value::Bool(b) => {
                        if *b {
                            "yes".to_string()
                        } else {
                            "no".to_string()
                        }
                    }
                    serde_json::Value::Number(n) => n.to_string(),
                    serde_json::Value::String(s) => s.clone(),
                    _ => v.to_string().trim_matches('"').to_string(),
                })
                .collect();

            let args_as_slices: Vec<&str> = string_args.iter().map(|s| s.as_str()).collect();

            if let Err(e) = instance.mpv.command(name, &args_as_slices) {
                let error_message = format!(
                    "Failed to execute mpv command '{}' with args '{:?}': {}",
                    name, args, e
                );
                error!("{}", error_message);
                return Err(crate::Error::Command(error_message));
            }

            Ok(())
        } else {
            error!("mpv instance for window label '{}' not found", window_label);
            Ok(())
        }
    }

    pub fn set_property(
        &self,
        name: &str,
        value: &serde_json::Value,
        window_label: &str,
    ) -> crate::Result<()> {
        trace!("SET PROPERTY '{}' '{:?}'", name, value);

        let instances_lock = match self.app.mpv().instances.lock() {
            Ok(guard) => guard,
            Err(poisoned) => {
                warn!("Mutex was poisoned, recovering.");
                poisoned.into_inner()
            }
        };

        if let Some(instance) = instances_lock.get(window_label) {
            let value_str = match value {
                serde_json::Value::Bool(b) => if *b { "yes" } else { "no" }.to_string(),
                serde_json::Value::Number(n) => n.to_string(),
                serde_json::Value::String(s) => s.clone(),
                serde_json::Value::Null => {
                    return Err(crate::Error::SetProperty(
                        "Cannot set property to null".to_string(),
                    ))
                }
                _ => {
                    return Err(crate::Error::SetProperty(format!(
                        "Unsupported value type for property '{}'",
                        name
                    )))
                }
            };

            match instance.mpv.set_property(name, value_str) {
                Ok(_) => Ok(()),
                Err(e) => Err(crate::Error::SetProperty(format!(
                    "Failed to set property '{}': {}",
                    name, e
                ))),
            }
        } else {
            Err(crate::Error::SetProperty(format!(
                "mpv instance for window label '{}' not found",
                window_label
            )))
        }
    }

    pub fn get_property(
        &self,
        name: String,
        format_str: Option<String>,
        window_label: &str,
    ) -> crate::Result<serde_json::Value> {
        let instances_lock = match self.app.mpv().instances.lock() {
            Ok(guard) => guard,
            Err(poisoned) => {
                warn!("Mutex was poisoned, recovering.");
                poisoned.into_inner()
            }
        };

        if let Some(instance) = instances_lock.get(window_label) {
            // 简化实现，只支持字符串格式
            let result = if let Some(ref s) = format_str {
                let _format = get_format_from_string(s)?; // 验证格式但暂时不使用
                instance.mpv.get_property::<String>(&name)
            } else {
                instance.mpv.get_property::<String>(&name)
            };

            let value = match result {
                Ok(val) => {
                    // 尝试解析为不同类型
                    if let Some(_format_str) = &format_str {
                        match _format_str.as_str() {
                            "flag" => {
                                let bool_val = val == "yes" || val == "true" || val == "1";
                                serde_json::Value::Bool(bool_val)
                            }
                            "int64" => val
                                .parse::<i64>()
                                .map(serde_json::Value::from)
                                .unwrap_or_else(|_| serde_json::Value::String(val)),
                            "double" => val
                                .parse::<f64>()
                                .map(serde_json::Value::from)
                                .unwrap_or_else(|_| serde_json::Value::String(val)),
                            _ => serde_json::Value::String(val),
                        }
                    } else {
                        serde_json::Value::String(val)
                    }
                }
                Err(e) => {
                    return Err(crate::Error::GetProperty(format!(
                        "Failed to get property '{}': {}",
                        name, e
                    )))
                }
            };

            trace!("GET PROPERTY '{}' '{:?}'", name, value);
            Ok(value)
        } else {
            Err(crate::Error::GetProperty(format!(
                "mpv instance for window label '{}' not found",
                window_label
            )))
        }
    }

    pub fn set_video_margin_ratio(
        &self,
        ratio: VideoMarginRatio,
        window_label: &str,
    ) -> Result<()> {
        trace!("SET VIDEO MARGIN RATIO '{:?}'", ratio);

        let app = self.app.clone();
        let instances_lock = match app.mpv().instances.lock() {
            Ok(guard) => guard,
            Err(poisoned) => {
                warn!("Mutex was poisoned, recovering.");
                poisoned.into_inner()
            }
        };

        if let Some(instance) = instances_lock.get(window_label) {
            let mpv = &instance.mpv;
            if let Err(e) = mpv.set_property("video-margin-ratio-left", ratio.left.unwrap_or(0.0)) {
                error!("Failed to set video margin ratio: {}", e);
            }
            if let Err(e) = mpv.set_property("video-margin-ratio-right", ratio.right.unwrap_or(0.0))
            {
                error!("Failed to set video margin ratio: {}", e);
            }
            if let Err(e) = mpv.set_property("video-margin-ratio-top", ratio.top.unwrap_or(0.0)) {
                error!("Failed to set video margin ratio: {}", e);
            }
            if let Err(e) =
                mpv.set_property("video-margin-ratio-bottom", ratio.bottom.unwrap_or(0.0))
            {
                error!("Failed to set video margin ratio: {}", e);
            }
        }
        Ok(())
    }
}
