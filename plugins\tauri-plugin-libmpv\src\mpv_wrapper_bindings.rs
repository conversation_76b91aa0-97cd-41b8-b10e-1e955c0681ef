use std::ffi::{CStr, CString};
use std::os::raw::{c_char, c_int, c_void};

// 错误代码定义
#[repr(C)]
#[derive(Debug, Clone, Copy, PartialEq)]
pub enum MpvWrapperError {
    Success = 0,
    Generic = -1,
    NoMem = -2,
    Uninitialized = -3,
    InvalidParameter = -4,
    OptionNotFound = -5,
    OptionFormat = -6,
    OptionError = -7,
    PropertyNotFound = -8,
    PropertyFormat = -9,
    PropertyUnavailable = -10,
    PropertyError = -11,
    Command = -12,
    LoadingFailed = -13,
    AoInitFailed = -14,
    VoInitFailed = -15,
    NothingToPlay = -16,
    UnknownFormat = -17,
    Unsupported = -18,
    NotImplemented = -19,
    GenericOpenFailed = -20,
}

// 数据格式枚举
#[repr(C)]
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, PartialEq)]
pub enum MpvWrapperFormat {
    None = 0,
    String = 1,
    OsdString = 2,
    Flag = 3,
    Int64 = 4,
    Double = 5,
    Node = 6,
    NodeArray = 7,
    NodeMap = 8,
    ByteArray = 9,
}

// 不透明句柄类型
#[repr(C)]
pub struct MpvWrapperHandle {
    _private: [u8; 0],
}

#[repr(C)]
pub struct MpvWrapperClient {
    _private: [u8; 0],
}

// 外部函数声明
extern "C" {
    pub fn mpv_wrapper_create() -> *mut MpvWrapperHandle;
    pub fn mpv_wrapper_initialize(ctx: *mut MpvWrapperHandle) -> c_int;
    pub fn mpv_wrapper_destroy(ctx: *mut MpvWrapperHandle);

    pub fn mpv_wrapper_set_option_string(
        ctx: *mut MpvWrapperHandle,
        name: *const c_char,
        data: *const c_char,
    ) -> c_int;

    pub fn mpv_wrapper_command_string(ctx: *mut MpvWrapperHandle, args: *const c_char) -> c_int;

    pub fn mpv_wrapper_command(ctx: *mut MpvWrapperHandle, args: *const *const c_char) -> c_int;

    pub fn mpv_wrapper_set_property_string(
        ctx: *mut MpvWrapperHandle,
        name: *const c_char,
        data: *const c_char,
    ) -> c_int;

    pub fn mpv_wrapper_get_property_string(
        ctx: *mut MpvWrapperHandle,
        name: *const c_char,
    ) -> *mut c_char;

    pub fn mpv_wrapper_get_property(
        ctx: *mut MpvWrapperHandle,
        name: *const c_char,
        format: c_int,
        data: *mut c_void,
    ) -> c_int;

    pub fn mpv_wrapper_free(data: *mut c_void);
    pub fn mpv_wrapper_error_string(error: c_int) -> *const c_char;
}

// 安全的Rust包装器
pub struct MpvWrapper {
    handle: *mut MpvWrapperHandle,
}

impl MpvWrapper {
    pub fn new() -> Option<Self> {
        unsafe {
            let handle = mpv_wrapper_create();
            if handle.is_null() {
                None
            } else {
                Some(Self { handle })
            }
        }
    }

    pub fn initialize(&self) -> Result<(), MpvWrapperError> {
        unsafe {
            let result = mpv_wrapper_initialize(self.handle);
            if result == MpvWrapperError::Success as c_int {
                Ok(())
            } else {
                Err(MpvWrapperError::from_i32(result).unwrap_or(MpvWrapperError::Generic))
            }
        }
    }

    pub fn set_option(&self, name: &str, value: &str) -> Result<(), MpvWrapperError> {
        unsafe {
            let name_cstr = CString::new(name).map_err(|_| MpvWrapperError::InvalidParameter)?;
            let value_cstr = CString::new(value).map_err(|_| MpvWrapperError::InvalidParameter)?;

            let result =
                mpv_wrapper_set_option_string(self.handle, name_cstr.as_ptr(), value_cstr.as_ptr());

            if result == MpvWrapperError::Success as c_int {
                Ok(())
            } else {
                Err(MpvWrapperError::from_i32(result).unwrap_or(MpvWrapperError::Generic))
            }
        }
    }

    pub fn command(&self, cmd: &str, args: &[&str]) -> Result<(), MpvWrapperError> {
        unsafe {
            // 构建命令字符串
            let mut cmd_string = cmd.to_string();
            for arg in args {
                cmd_string.push(' ');
                cmd_string.push_str(arg);
            }

            let cmd_cstr =
                CString::new(cmd_string).map_err(|_| MpvWrapperError::InvalidParameter)?;
            let result = mpv_wrapper_command_string(self.handle, cmd_cstr.as_ptr());

            if result == MpvWrapperError::Success as c_int {
                Ok(())
            } else {
                Err(MpvWrapperError::from_i32(result).unwrap_or(MpvWrapperError::Generic))
            }
        }
    }

    pub fn set_property<T>(&self, name: &str, value: T) -> Result<(), MpvWrapperError>
    where
        T: ToString,
    {
        unsafe {
            let name_cstr = CString::new(name).map_err(|_| MpvWrapperError::InvalidParameter)?;
            let value_cstr =
                CString::new(value.to_string()).map_err(|_| MpvWrapperError::InvalidParameter)?;

            let result = mpv_wrapper_set_property_string(
                self.handle,
                name_cstr.as_ptr(),
                value_cstr.as_ptr(),
            );

            if result == MpvWrapperError::Success as c_int {
                Ok(())
            } else {
                Err(MpvWrapperError::from_i32(result).unwrap_or(MpvWrapperError::Generic))
            }
        }
    }

    pub fn get_property<T>(&self, name: &str) -> Result<T, MpvWrapperError>
    where
        T: std::str::FromStr,
        T::Err: std::fmt::Debug,
    {
        unsafe {
            let name_cstr = CString::new(name).map_err(|_| MpvWrapperError::InvalidParameter)?;
            let result_ptr = mpv_wrapper_get_property_string(self.handle, name_cstr.as_ptr());

            if result_ptr.is_null() {
                return Err(MpvWrapperError::PropertyNotFound);
            }

            let result_cstr = CStr::from_ptr(result_ptr);
            let result_str = result_cstr
                .to_str()
                .map_err(|_| MpvWrapperError::PropertyFormat)?;
            let value = result_str
                .parse::<T>()
                .map_err(|_| MpvWrapperError::PropertyFormat)?;

            mpv_wrapper_free(result_ptr as *mut c_void);
            Ok(value)
        }
    }
}

impl Drop for MpvWrapper {
    fn drop(&mut self) {
        unsafe {
            if !self.handle.is_null() {
                mpv_wrapper_destroy(self.handle);
            }
        }
    }
}

unsafe impl Send for MpvWrapper {}
unsafe impl Sync for MpvWrapper {}

impl MpvWrapperError {
    pub fn from_i32(value: c_int) -> Option<Self> {
        match value {
            0 => Some(Self::Success),
            -1 => Some(Self::Generic),
            -2 => Some(Self::NoMem),
            -3 => Some(Self::Uninitialized),
            -4 => Some(Self::InvalidParameter),
            -5 => Some(Self::OptionNotFound),
            -6 => Some(Self::OptionFormat),
            -7 => Some(Self::OptionError),
            -8 => Some(Self::PropertyNotFound),
            -9 => Some(Self::PropertyFormat),
            -10 => Some(Self::PropertyUnavailable),
            -11 => Some(Self::PropertyError),
            -12 => Some(Self::Command),
            -13 => Some(Self::LoadingFailed),
            -14 => Some(Self::AoInitFailed),
            -15 => Some(Self::VoInitFailed),
            -16 => Some(Self::NothingToPlay),
            -17 => Some(Self::UnknownFormat),
            -18 => Some(Self::Unsupported),
            -19 => Some(Self::NotImplemented),
            -20 => Some(Self::GenericOpenFailed),
            _ => None,
        }
    }
}

impl std::fmt::Display for MpvWrapperError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        unsafe {
            let error_cstr = CStr::from_ptr(mpv_wrapper_error_string(*self as c_int));
            let error_str = error_cstr.to_str().unwrap_or("Unknown error");
            write!(f, "{}", error_str)
        }
    }
}

impl std::error::Error for MpvWrapperError {}
